设计一个现代化的 UI 界面，具体要求如下：

**整体风格：**

- 采用现代简约的扁平化设计风格
- 使用卡片式布局，界面简洁清爽
- 渐进式引导设计，通过步骤指示器展示创建流程

**色彩方案：**

- 主色调：蓝色系（#1890ff）作为主要品牌色
- 成功状态：绿色（#52c41a）
- 警告状态：橙色/黄色
- 背景：浅灰色背景（#f5f7fa），白色卡片容器
- 文字：深灰色主文字（#262626），浅灰色辅助文字（#8c8c8c）

**组件设计：**

- 步骤指示器：圆形彩色图标 + 连接线
- 卡片容器：白色背景，圆角边框，轻微阴影
- 按钮：圆角设计，主要按钮使用品牌蓝色
- 输入框：简洁边框，聚焦时蓝色高亮
- 进度条：彩色进度指示

**交互效果：**

- 悬浮效果：按钮和卡片 hover 时轻微上移和阴影加深
- 过渡动画：所有状态变化都有平滑的过渡效果
- 响应式设计：支持桌面和移动端适配

**图标风格：**

- 使用线性图标风格
- 圆形背景容器
- 颜色与功能状态对应（绿色=完成，蓝色=当前，灰色=未开始）

**字体排版：**

- 标题：16px，粗体，深灰色
- 正文：13-14px，常规，深灰色
- 辅助文字：12px，浅灰色
- 行间距适中，保证良好的可读性

请基于以上要求设计一个专业、现代、用户友好的界面。
